# XAUUSD Smart Trader EA 测试指南

## 测试前准备

### 1. 环境要求
- MetaTrader 4 平台
- XAUUSD 交易品种
- 建议先在模拟账户测试

### 2. 安装步骤
1. 将 `XAUUSD_SmartTrader.mq4` 复制到 MT4 的 `MQL4/Experts` 文件夹
2. 重启 MT4 或按 F4 刷新导航器
3. 在导航器中找到 "XAUUSD_SmartTrader" EA
4. 拖拽到 XAUUSD 图表上

### 3. 参数设置建议
```
MaxLossUSD = 50.0          // 建议从小金额开始测试
RiskRewardRatio = 2.0      // 标准盈亏比
DefaultSLDistancePoints = 300  // 适中的止损距离
MinLots = 0.01            // 最小手数
MaxLots = 1.0             // 测试时限制最大手数
ShowInfoPanel = true      // 显示信息面板
```

## 功能测试清单

### 1. 基础功能测试
- [ ] EA 成功加载并初始化
- [ ] 图表上显示止盈止损线
- [ ] 显示交易按钮（买入、卖出、重置）
- [ ] 信息面板正确显示

### 2. 交易方向测试
- [ ] 做多按钮正常工作
- [ ] 做空按钮正常工作
- [ ] 做多模式：止损线在价格下方，止盈线在价格上方
- [ ] 做空模式：止损线在价格上方，止盈线在价格下方
- [ ] 模式切换时线条自动重新定位
- [ ] 按钮状态正确显示（高亮当前模式）

### 3. 线条拖动测试
- [ ] 止损线可以拖动
- [ ] 拖动止损线时止盈线自动调整
- [ ] 止盈线可以拖动
- [ ] 拖动止盈线时盈亏比更新
- [ ] 价格标签正确显示

### 4. 鼠标控制测试
- [ ] L键选中止损线功能正常
- [ ] T键选中止盈线功能正常
- [ ] 鼠标移动时线条跟随
- [ ] Enter键确认功能正常
- [ ] ESC键取消功能正常
- [ ] 线条高亮显示正确

### 5. 计算功能测试
- [ ] 手数计算正确
- [ ] 盈亏比计算准确
- [ ] 预期盈亏显示正确
- [ ] 保证金检查有效
- [ ] 止盈线实时跟随价格变动

### 6. 交易功能测试
- [ ] 买入按钮正常工作
- [ ] 卖出按钮正常工作
- [ ] 订单正确设置止盈止损
- [ ] 持仓时按钮状态更新
- [ ] 持仓时线条锁定

### 7. 保本功能测试
- [ ] 保本按钮在持仓时可用
- [ ] 保本按钮在无持仓时禁用
- [ ] 保本功能正确执行
- [ ] 止损价格正确调整到开仓价
- [ ] 盈利不足时拒绝保本

### 8. 安全检查测试
- [ ] 保证金不足时拒绝交易
- [ ] 手数超限时调整
- [ ] 止损距离过小时警告
- [ ] 重复下单防护有效

### 9. 用户界面测试
- [ ] 信息面板实时更新
- [ ] 快捷键功能正常（1=做多, 2=做空, B=买入, S=卖出, R=重置, Q=保本）
- [ ] 按钮点击响应正常
- [ ] 按钮位置正确（左下角）
- [ ] 确认对话框显示

## 测试场景

### 场景1：正常交易流程
1. 启动EA，观察初始状态
2. 选择交易方向（做多/做空）
3. 调整止损线位置（拖动或L键鼠标控制）
4. 观察止盈线实时跟随价格变动
5. 确认手数和盈亏比合理
6. 点击买入或卖出按钮
7. 确认订单执行成功
8. 观察持仓状态显示
9. 测试保本功能（Q键或点击保本按钮）

### 场景2：参数调整测试
1. 修改最大亏损金额
2. 拖动线条观察手数变化
3. 调整盈亏比设置
4. 验证计算结果正确性

### 场景3：错误处理测试
1. 设置极小的保证金
2. 尝试交易观察错误提示
3. 设置过大的手数
4. 验证安全检查机制

### 场景4：界面交互测试
1. 测试所有按钮功能
2. 测试快捷键操作
3. 测试线条拖动响应
4. 验证信息面板更新

## 性能测试

### 1. 响应速度测试
- 线条拖动响应时间 < 100ms
- 按钮点击响应时间 < 50ms
- 信息面板更新频率适中

### 2. 稳定性测试
- 长时间运行无内存泄漏
- 网络断线重连后正常工作
- 图表切换后对象保持正常

### 3. 兼容性测试
- 不同时间框架下正常工作
- 与其他EA共存无冲突
- 不同屏幕分辨率下显示正常

## 常见问题排查

### 问题1：EA无法加载
- 检查文件路径是否正确
- 确认MT4允许EA运行
- 查看专家日志错误信息

### 问题2：线条无法拖动
- 确认图表允许对象拖动
- 检查是否处于持仓状态
- 重新初始化EA

### 问题3：交易失败
- 检查账户余额和保证金
- 确认市场开放时间
- 验证交易权限设置

### 问题4：信息显示异常
- 刷新图表重新显示
- 检查EA参数设置
- 重启EA重新初始化

## 测试报告模板

```
测试日期：____
测试环境：____
测试账户：模拟/真实
测试品种：XAUUSD

基础功能：通过/失败
交易功能：通过/失败
安全检查：通过/失败
用户界面：通过/失败

发现问题：
1. ____
2. ____

改进建议：
1. ____
2. ____

总体评价：____
```

## 注意事项

1. **风险提示**：请先在模拟账户充分测试
2. **参数设置**：根据个人风险承受能力调整参数
3. **市场条件**：注意测试不同市场条件下的表现
4. **定期检查**：定期检查EA运行状态和日志
5. **备份设置**：保存测试中的最佳参数配置

## 技术支持

如在测试过程中遇到问题，请：
1. 查看MT4专家日志
2. 记录具体操作步骤
3. 截图保存错误信息
4. 联系技术支持团队
